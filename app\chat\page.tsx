"use client";
import SmallDisplay from "@/components/small-display";
import { ChatForm } from "@/components/chat";
import { Button } from "@/components/ui/button";
import { useManageChat, Message } from "@/hooks/use-manage-chat";
import { useEffect, useState } from "react";
import { TextSelectionActions } from "@/components/text-selection";
import { useCallback } from "react";
import { basicActions, actions } from "@/lib/actions-text-selections";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from 'date-fns';
import { Trash2, RefreshCw, PlusSquare, Settings, Filter, Upload } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";

// Types
type Tag = {
  id: string;
  name: string;
};

type Topic = {
  id: string;
  value: string;
  weight: number;
  type: string;
  tags: Tag[];
};

export default function Page() {
  const { startNewChat, isOpen, toggleOpen, setMessages, setChatSectionId, setSelectedGrammarRulesArray, setSelectedTopicsArray, setGrammarRuleCount } = useManageChat();
  const [chatSections, setChatSections] = useState<any[]>([]);

  // Topics management state
  const [topics, setTopics] = useState<Topic[]>([]);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [selectedFilterTags, setSelectedFilterTags] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [newTopicValue, setNewTopicValue] = useState('');
  const [newTopicWeight, setNewTopicWeight] = useState(10);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [bulkJsonInput, setBulkJsonInput] = useState('');
  const [showBulkUpload, setShowBulkUpload] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    toggleOpen(false);
    fetchChatSections();
    fetchTopics();
    fetchTags();
  }, []);

  const fetchChatSections = async () => {
    try {
      const response = await fetch('/api/chat-sections');
      if (!response.ok) {
        throw new Error('Failed to fetch chat sections');
      }
      const data = await response.json();
      setChatSections(data);
    } catch (error) {
      console.error("Error fetching chat sections:", error);
    }
  };

  const fetchTopics = async () => {
    try {
      const response = await fetch('/api/custom-settings?type=topic');
      if (!response.ok) {
        throw new Error('Failed to fetch topics');
      }
      const data: Topic[] = await response.json();
      setTopics(data);
    } catch (error) {
      console.error("Error fetching topics:", error);
      toast({
        title: "Error",
        description: "Failed to fetch topics.",
        variant: "destructive",
      });
    }
  };

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/custom-settings/tags');
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }
      const data: Tag[] = await response.json();
      setAllTags(data);
    } catch (error) {
      console.error("Error fetching tags:", error);
      toast({
        title: "Error",
        description: "Failed to fetch tags.",
        variant: "destructive",
      });
    }
  };

  const handleCreateTopic = async () => {
    if (!newTopicValue.trim()) {
      toast({
        title: "Validation Error",
        description: "Topic value cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/custom-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'topic', value: newTopicValue, weight: newTopicWeight, tagIds: selectedTagIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to save topic');
      }

      toast({
        title: "Success",
        description: "Topic created successfully.",
      });
      setNewTopicValue('');
      setNewTopicWeight(10);
      setSelectedTagIds([]);
      fetchTopics();
    } catch (error) {
      console.error("Error saving topic:", error);
      toast({
        title: "Error",
        description: `Failed to save topic: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleBulkUploadTopics = async () => {
    if (!bulkJsonInput.trim()) {
      toast({
        title: "Validation Error",
        description: "Bulk upload JSON cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const topicsToUpload = JSON.parse(bulkJsonInput);
      if (!Array.isArray(topicsToUpload)) {
        throw new Error("Input is not a valid JSON array of topics.");
      }

      const uploadPromises = topicsToUpload.map(async (topic: any) => {
        if (!topic.value || typeof topic.value !== 'string' || !topic.weight || typeof topic.weight !== 'number') {
          throw new Error("Each topic must have a 'value' (string) and 'weight' (number).");
        }
        const topicBody = { ...topic, type: 'topic' };

        const response = await fetch('/api/custom-settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(topicBody),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to upload topic '${topic.value}': ${errorData.message || response.statusText}`);
        }
        return response.json();
      });

      await Promise.all(uploadPromises);

      toast({
        title: "Success",
        description: "All topics uploaded successfully.",
      });
      setBulkJsonInput('');
      setShowBulkUpload(false);
      fetchTopics();
    } catch (error) {
      console.error("Error bulk uploading topics:", error);
      toast({
        title: "Error",
        description: `Failed to bulk upload topics: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleDeleteTopic = async (id: string) => {
    try {
      const response = await fetch(`/api/custom-settings/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete topic');
      }

      toast({
        title: "Success",
        description: "Topic deleted successfully.",
      });
      fetchTopics();
    } catch (error) {
      console.error("Error deleting topic:", error);
      toast({
        title: "Error",
        description: `Failed to delete topic: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const filteredTopics = topics.filter(topic => {
    if (selectedFilterTags.length === 0) return true;
    return topic.tags.some(tag => selectedFilterTags.includes(tag.id));
  });

  const loadChatSection = (section: any) => {
    const loadedMessages: Message[] = section.messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
      correction: msg.correction || undefined, // Load correction data
      suggestion: msg.suggestion || undefined, // Load suggestion data
    }));
    setMessages(loadedMessages);
    setChatSectionId(section.id);
    setSelectedGrammarRulesArray(section.grammarRuleSections.map((grs: any) => ({
      value: grs.grammarRule.value,
      weight: grs.weight,
      tags: grs.grammarRule.tags,
    })));
    // Set selected topics
    setSelectedTopicsArray(section.chatSectionTopics.map((cst: any) => cst.customChatSetting.value));
    // Set grammar rule count
    setGrammarRuleCount(section.grammarRuleCount || 1);
  };

  const handleDeleteSection = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this chat section?")) {
      try {
        const response = await fetch(`/api/chat-sections/${id}`, {
          method: 'DELETE',
        });
        if (!response.ok) {
          throw new Error('Failed to delete chat section');
        }
        fetchChatSections(); // Re-fetch sections after deletion
        // Clear current chat if the deleted section was the one currently loaded
        setMessages([]);
        setChatSectionId(null);
        setSelectedGrammarRulesArray([]);
        setSelectedTopicsArray([]);
        setGrammarRuleCount(1);
      } catch (error) {
        console.error("Error deleting chat section:", error);
      }
    }
  };

  return (
    <div className="flex h-svh w-full">
      {/* Left Column: Past Chat Sections */}
      <div className="w-1/4 border-r p-4 flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Past Chats</h2>
          <div>
            <Button variant="ghost" size="icon" onClick={() => startNewChat()}>
              <PlusSquare className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={fetchChatSections}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <ScrollArea className="flex-1 pr-2">
          {chatSections.length === 0 ? (
            <p className="text-sm text-muted-foreground">No past chat sections found.</p>
          ) : (
            <div className="space-y-3">
              {chatSections.map((section) => (
                <Card key={section.id} className="group relative cursor-pointer hover:bg-accent">
                  <div onClick={() => loadChatSection(section)}>
                    <CardHeader className="p-3 pb-1">
                      <CardTitle className="text-base truncate">{section.content || `Chat ${format(new Date(section.createdAt), 'PPP p')}`}</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 pt-0 text-sm text-muted-foreground">
                      <p>{new Date(section.createdAt).toLocaleDateString()}</p>
                      {section.grammarRuleSections.length > 0 && (
                        <div className="mt-1 text-xs">
                          <span className="font-medium">Rules:</span> {section.grammarRuleSections.map((grs: any) => grs.grammarRule.value).join(', ')}
                        </div>
                      )}
                      {section.chatSectionTopics.length > 0 && (
                        <div className="mt-1 text-xs">
                          <span className="font-medium">Topics:</span> {section.chatSectionTopics.map((cst: any) => cst.customChatSetting.value).join(', ')}
                        </div>
                      )}
                    </CardContent>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleDeleteSection(section.id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Middle Column: Topics Management */}
      <div className="w-1/3 border-r p-4 flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Topics</h2>
          <div className="flex gap-1">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[200px] p-0">
                <div className="flex flex-col p-1">
                  <div className="p-2 font-medium text-sm">Filter by Tags</div>
                  {allTags.map((tag) => (
                    <div
                      key={tag.id}
                      className="flex items-center space-x-2 py-1.5 px-2 rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground"
                      onClick={() => {
                        setSelectedFilterTags(prev =>
                          prev.includes(tag.id)
                            ? prev.filter(id => id !== tag.id)
                            : [...prev, tag.id]
                        );
                      }}
                    >
                      <Checkbox
                        id={`filter-tag-${tag.id}`}
                        checked={selectedFilterTags.includes(tag.id)}
                      />
                      <Label htmlFor={`filter-tag-${tag.id}`}>{tag.name}</Label>
                    </div>
                  ))}
                  {selectedFilterTags.length > 0 && (
                    <>
                      <div className="h-px bg-border my-1" />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedFilterTags([])}
                        className="mx-2"
                      >
                        Clear Filters
                      </Button>
                    </>
                  )}
                </div>
              </PopoverContent>
            </Popover>
            <Button variant="ghost" size="icon" onClick={() => setShowBulkUpload(!showBulkUpload)}>
              <Upload className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Bulk Upload Section */}
        {showBulkUpload && (
          <Card className="mb-4">
            <CardHeader className="p-3">
              <CardTitle className="text-sm">Bulk Upload Topics</CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-0">
              <div className="space-y-2">
                <textarea
                  value={bulkJsonInput}
                  onChange={(e) => setBulkJsonInput(e.target.value)}
                  placeholder={`[{"value": "Travel", "weight": 10, "tagNames": ["leisure"]}]`}
                  rows={4}
                  className="flex min-h-[60px] w-full rounded-md border border-input bg-background px-2 py-1 text-xs ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleBulkUploadTopics}>Upload</Button>
                  <Button size="sm" variant="outline" onClick={() => setShowBulkUpload(false)}>Cancel</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Add New Topic Section */}
        <Card className="mb-4">
          <CardHeader className="p-3">
            <CardTitle className="text-sm">Add Topic</CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="space-y-2">
              <Input
                value={newTopicValue}
                onChange={(e) => setNewTopicValue(e.target.value)}
                placeholder="Topic name"
                className="text-xs"
              />
              <div className="flex gap-2">
                <Input
                  type="number"
                  value={newTopicWeight}
                  onChange={(e) => setNewTopicWeight(parseInt(e.target.value) || 0)}
                  placeholder="Weight"
                  className="text-xs w-20"
                  min="0"
                />
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="text-xs">
                      Tags ({selectedTagIds.length})
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[180px] p-0">
                    <div className="flex flex-col p-1">
                      {allTags.map((tag) => (
                        <div
                          key={tag.id}
                          className="flex items-center space-x-2 py-1.5 px-2 rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground"
                          onClick={() => {
                            setSelectedTagIds(prev =>
                              prev.includes(tag.id)
                                ? prev.filter(id => id !== tag.id)
                                : [...prev, tag.id]
                            );
                          }}
                        >
                          <Checkbox
                            id={`tag-${tag.id}`}
                            checked={selectedTagIds.includes(tag.id)}
                          />
                          <Label htmlFor={`tag-${tag.id}`} className="text-xs">{tag.name}</Label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              <Button size="sm" onClick={handleCreateTopic} className="w-full">Add Topic</Button>
            </div>
          </CardContent>
        </Card>

        {/* Topics List */}
        <ScrollArea className="flex-1">
          <div className="space-y-2">
            {filteredTopics.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                {selectedFilterTags.length > 0 ? 'No topics match the selected filters.' : 'No topics found.'}
              </p>
            ) : (
              filteredTopics.map((topic) => (
                <Card key={topic.id} className="group relative">
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedTopics.includes(topic.value)}
                            onCheckedChange={() => {
                              const newSelectedTopics = selectedTopics.includes(topic.value)
                                ? selectedTopics.filter(t => t !== topic.value)
                                : [...selectedTopics, topic.value];

                              setSelectedTopics(newSelectedTopics);
                              // Update the global state
                              setSelectedTopicsArray(newSelectedTopics);
                            }}
                          />
                          <div>
                            <p className="text-sm font-medium">{topic.value}</p>
                            <p className="text-xs text-muted-foreground">Weight: {topic.weight}</p>
                          </div>
                        </div>
                        {topic.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {topic.tags.map(tag => (
                              <Badge key={tag.id} variant="secondary" className="text-xs">{tag.name}</Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
                        onClick={() => handleDeleteTopic(topic.id)}
                      >
                        <Trash2 className="h-3 w-3 text-red-500" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Right Column: Chat Interface */}
      <div className="flex flex-col h-full flex-1 items-center">
        <div className="flex flex-col h-full w-[90%]"> {/* Adjusted width for chat form */}
          <div className="flex-grow p-2">
            <SmallDisplay />
          </div>
          <div className="h-[87%]">
            <ChatForm />
          </div>
        </div>
      </div>
    </div>
  );
}
