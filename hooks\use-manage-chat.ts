"use client"; // Ensure this runs on the client side

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { correctionSchema } from "@/lib/schemas";
import {z} from "zod"

type Correction = z.infer<typeof correctionSchema>

export type Suggestion = {
  grammar_rule: string;
  next_response_ideas: string[];
}

export type Message = {
    role: 'user' | 'assistant'
    content: string,
    correction?:Correction,
    attached?:string,
    suggestion?: Suggestion
  };

type ChatState = {
  messages: Message[];
  input: string;
  isLoading: boolean;
  isOpen: boolean;
  selectedTopics: string[];
  selectedGrammarRules: { value: string; weight: number; tags: { id: string; name: string }[] }[];
  chatSectionId: string | null; // New state for chat section ID
  grammarRuleCount: number; // Number of grammar rules to select
  toggleOpen:(i:boolean)=>void,
  addCorrection: (correction: Correction) => void;
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  setInput: (input: string) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsOpen: () => void;
  setSelectedTopics: (topic: string) => void;
  setSelectedGrammarRules: (ruleObj: { value: string; weight: number; tags: { id: string; name: string }[] }) => void;
  setSelectedGrammarRulesArray: (rules: { value: string; weight: number; tags: { id: string; name: string }[] }[]) => void;
  setChatSectionId: (id: string | null) => void; // New setter for chat section ID
  setSelectedTopicsArray: (topics: string[]) => void; // New setter for selected topics array
  setGrammarRuleCount: (count: number) => void; // New setter for grammar rule count
  startNewChat: () => void; // Function to start a new chat
};

export const useManageChat= create<ChatState>()(
  devtools((set) => ({
    messages: [],
    input: "",
    isLoading: false,
    isOpen: true,
    selectedTopics: [],
    selectedGrammarRules: [],
    chatSectionId: null, // Initialize chatSectionId
    grammarRuleCount: 1, // Initialize with default value of 1
    addCorrection: (correction) =>
      set((state) =>{
        
      const lastMessage = state.messages[state.messages.length - 1];
      if(lastMessage.role === "assistant"){
        return ({messages:state.messages})
      }
       lastMessage.correction = correction

       const neWmessages = state.messages
       neWmessages[state.messages.length - 1] = lastMessage

       return ({ messages: neWmessages })
      
      }),
      
    setMessages: (messages) => set({ messages }),
    addMessage: (message) =>
      set((state) => ({ messages: [...state.messages, message] })),
    setInput: (input) => set({ input }),
    setIsLoading: (isLoading) => set({ isLoading }),
    setIsOpen: () => set((state) => ({ isOpen: !state.isOpen })),
    toggleOpen: (isOpen) => set({ isOpen }),
    setSelectedTopics: (topic) => set((state) => {
      const newSelectedTopics = state.selectedTopics.includes(topic)
        ? state.selectedTopics.filter((t) => t !== topic)
        : [...state.selectedTopics, topic];
      return { selectedTopics: newSelectedTopics };
    }),
    setSelectedGrammarRules: (ruleObj: { value: string; weight: number; tags: { id: string; name: string }[] }) => set((state) => {
      const newSelectedGrammarRules = state.selectedGrammarRules.some(r => r.value === ruleObj.value)
        ? state.selectedGrammarRules.filter((r) => r.value !== ruleObj.value)
        : [...state.selectedGrammarRules, ruleObj];
      return { selectedGrammarRules: newSelectedGrammarRules };
    }),
    setSelectedGrammarRulesArray: (rules) => set({ selectedGrammarRules: rules }),
    setChatSectionId: (id) => set({ chatSectionId: id }), // Implement new setter
    setSelectedTopicsArray: (topics) => set({ selectedTopics: topics }), // Implement new setter
    setGrammarRuleCount: (count) => set({ grammarRuleCount: count }), // Implement new setter
    startNewChat: () => set({
      messages: [],
      chatSectionId: null,
      selectedTopics: [],
      selectedGrammarRules: [],
    }),
  }))
);
