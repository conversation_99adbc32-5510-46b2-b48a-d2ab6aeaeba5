'use client'

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils'
import { useManageChat, Message, Suggestion } from '@/hooks/use-manage-chat' // Import Message and Suggestion
import { ArrowUpIcon, Divide, CheckIcon, Lightbulb, CircleAlert, X, PlusCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip'
import { AutoResizeTextarea } from './autoresize-textarea'
import MarkdownTypewriter from './markdowntyper'
import { correctionSchema } from "@/lib/schemas";
import { Input } from './ui/input';
import {z} from "zod"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,

} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter

} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import TextSelectionActions from './text-selection'
import { Badge } from './ui/badge'
import { basicActions, actions } from "@/lib/actions-text-selections";


type Correction = z.infer<typeof correctionSchema>
// Removed local Suggestion and Message types, now imported from use-manage-chat


export function ChatForm({
  className,
  ...props
}: React.ComponentProps<'form'>) {
  const { messages, input, setInput, addMessage, setIsLoading, isLoading, addCorrection, selectedTopics, setSelectedTopics, selectedGrammarRules, setSelectedGrammarRules, setSelectedGrammarRulesArray, chatSectionId, setChatSectionId, grammarRuleCount, setGrammarRuleCount } = useManageChat()
  const [newGrammarRule, setNewGrammarRule] = useState('');
  const [newGrammarRuleWeight, setNewGrammarRuleWeight] = useState(10); // Default weight

  const [allTopics, setAllTopics] = useState(["General", "Travel", "Food", "Technology", "Sports", "Education", "Health", "Art", "Science", "History"]);
  type GrammarRuleSetting = { value: string; weight: number; tags: { id: string; name: string }[] };
  type TopicSetting = { id: string; value: string; weight: number; tags: { id: string; name: string }[] };
  const [allGrammarRules, setAllGrammarRules] = useState<GrammarRuleSetting[]>([]);
  const [allTopicsWithTags, setAllTopicsWithTags] = useState<TopicSetting[]>([]);

  // States for grammar rule dialog
  const [allTags, setAllTags] = useState<{ id: string; name: string }[]>([]);
  const [selectedFilterTags, setSelectedFilterTags] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // States for topic dialog
  const [selectedTopicFilterTags, setSelectedTopicFilterTags] = useState<string[]>([]);
  const [topicSearchTerm, setTopicSearchTerm] = useState('');


  useEffect(() => {
    const fetchCustomSettings = async () => {
      try {
        const [topicsRes, rulesRes, tagsRes] = await Promise.all([
          fetch('/api/custom-settings?type=topic'),
          fetch('/api/custom-settings?type=grammarRule'),
          fetch('/api/custom-settings/tags') // Fetch tags
        ]);

        const topicsData = await topicsRes.json();
        const rulesData = await rulesRes.json();
        const tagsData = await tagsRes.json(); // Get tags data

        const initialTopics = ["General", "Travel", "Food", "Technology", "Sports", "Education", "Health", "Art", "Science", "History"];
        const fetchedTopics = topicsData.map((s: any) => s.value);
        setAllTopics([...new Set([...initialTopics, ...fetchedTopics])]);

        const topicsWithTags = topicsData.map((s: any) => ({ id: s.id, value: s.value, weight: s.weight, tags: s.tags }));
        setAllTopicsWithTags(topicsWithTags);

        const grammarRules = rulesData.map((s: any) => ({ value: s.value, weight: s.weight, tags: s.tags }));
        setAllGrammarRules(grammarRules);
        
        setAllTags(tagsData); // Set tags state, overwriting previous state.
      } catch (error) {
        console.error("Failed to fetch custom settings:", error);
      }
    };

    fetchCustomSettings();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim()) return

    const userMessage: Message = { role: 'user', content: input }
    addMessage(userMessage)
    setInput('')
    setIsLoading(true)

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ input, messages, selectedTopics, selectedGrammarRules: selectedGrammarRules.map(r => ({ value: r.value, weight: r.weight })), chatSectionId, grammarRuleCount }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()
      console.log(data)
      addCorrection(data.correctionResponse)
      const assistantMessage: Message = {
        role: 'assistant',
        content: data.vocabularyResponse.message,
        suggestion: data.vocabularyResponse.suggestion
      }
      addMessage( assistantMessage)
      if (data.chatSectionId) {
        setChatSectionId(data.chatSectionId);
      }
    } catch (error) {
      console.error('Error:', error)
      // Handle error (e.g., show an error message to the user)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as unknown as React.FormEvent<HTMLFormElement>)
    }
  }

  const handleInputChange = (value: string) => {
    setInput(value)
  }

  // Tag filter logic
  const handleTagFilterToggle = (tagName: string) => {
    setSelectedFilterTags(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  const handleSelectByTag = (tagName: string) => {
    const rulesForTag = allGrammarRules.filter(rule => 
      rule.tags.some(tag => tag.name === tagName) &&
      !selectedGrammarRules.some(selected => selected.value === rule.value)
    );
    setSelectedGrammarRulesArray([...selectedGrammarRules, ...rulesForTag]);
  };

  const availableGrammarRules = allGrammarRules.filter(rule => {
    const isSelected = selectedGrammarRules.some(selected => selected.value === rule.value);
    if (isSelected) return false;

    const tagMatch = selectedFilterTags.length === 0 || rule.tags.some(tag => selectedFilterTags.includes(tag.name));
    const searchMatch = rule.value.toLowerCase().includes(searchTerm.toLowerCase());

    return tagMatch && searchMatch;
  });

  // Topic filter logic
  const handleTopicTagFilterToggle = (tagName: string) => {
    setSelectedTopicFilterTags(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  const handleSelectTopicsByTag = (tagName: string) => {
    const topicsForTag = allTopicsWithTags.filter(topic =>
      topic.tags.some(tag => tag.name === tagName) &&
      !selectedTopics.includes(topic.value)
    );
    const newSelectedTopics = [...selectedTopics, ...topicsForTag.map(t => t.value)];
    setSelectedTopicsArray(newSelectedTopics);
  };

  const availableTopics = allTopicsWithTags.filter(topic => {
    const isSelected = selectedTopics.includes(topic.value);
    if (isSelected) return false;

    const tagMatch = selectedTopicFilterTags.length === 0 || topic.tags.some(tag => selectedTopicFilterTags.includes(tag.name));
    const searchMatch = topic.value.toLowerCase().includes(topicSearchTerm.toLowerCase());

    return tagMatch && searchMatch;
  });

  const handleAddAllVisibleTopics = () => {
    const newTopics = availableTopics.map(t => t.value);
    setSelectedTopicsArray([...selectedTopics, ...newTopics]);
  };

  const handleDeselectAllTopics = () => {
    setSelectedTopicsArray([]);
  };

  const handleAddAllVisibleRules = () => {
    const newSelection = [...selectedGrammarRules];
    availableGrammarRules.forEach(rule => {
      if (!newSelection.some(selected => selected.value === rule.value)) {
        newSelection.push(rule);
      }
    });
    setSelectedGrammarRulesArray(newSelection);
  };

  const handleDeselectAllRules = () => {
    setSelectedGrammarRulesArray([]);
  };


  const header = (
    <header className="m-auto flex max-w-96 flex-col gap-5 text-center">
      <h1 className="text-2xl font-semibold leading-none tracking-tight">
        Basic AI Chatbot Template (Non-Streaming)
      </h1>
      <p className="text-muted-foreground text-sm">
        This is an AI chatbot app template built with{' '}
        <span className="text-foreground">Next.js</span> and the{' '}
        <span className="text-foreground">Vercel AI SDK</span>, using a non-streaming approach.
      </p>
      <p className="text-muted-foreground text-sm">
        Send a message to get started.
      </p>
    </header>
  )
  

  const messageList = (
    <div className="pt-2 flex h-fit min-h-full flex-col gap-4">
      {messages?.map((message, index) => (
      <div  
        data-role={message.role}
        className=' flex flex-col items-center justify-center max-w-[90%] data-[role=assistant]:self-start data-[role=user]:self-end'
      >
        {message.attached && <div className='self-end my-1' ><Badge>{message.attached}</Badge></div>}
        <div
          key={index}
          data-role={message.role}
          className="justify-self-end w-fit relative rounded-xl px-3 py-2 text-sm data-[role=assistant]:self-start data-[role=user]:self-end data-[role=assistant]:bg-gray-100 data-[role=user]:bg-slate-600 data-[role=assistant]:text-black data-[role=user]:text-white"
        >
          
            <MarkdownTypewriter  content={message.content} typeSpeed={1} />
            {message.correction?.correctedSentence?.error && (<div className="my-1 italic flex items-center gap-1 font-light py-1 text-xs text-green-50 bg-slate-700 px-2 rounded">{message.correction?.correctedSentence?.sentence}
              <Popover>
                <PopoverTrigger className='text-white'><CircleAlert className='text-slate-50'  size={14} /></PopoverTrigger>
                <PopoverContent>
                  {message.correction?.correctedSentence?.explanation}
                </PopoverContent>
              </Popover>   
          </div>)}
            {message.correction  && message.correction?.correctedSentence?.error ? (<button className={`absolute bottom-1 right-2 h-1 w-1 rounded-full bg-green-500 `}/>) : (<button className={`absolute bottom-1 right-2 h-1 w-1 rounded-full bg-slate-50 `}/>) }
        </div>
        {
          message.correction && 
          (
            <div className='text-xs  italic border rounded-xl p-1 mt-1' >
              <Carousel >
                <CarouselContent >
                  {
                    message.correction?.possibilties?.map((correction, index) => (
                      <CarouselItem className='flex items-center justify-center' key={index}>
                        {correction} 
                        
                      </CarouselItem>
                    ))
                  }
                </CarouselContent>
                <CarouselPrevious className='size-3 -left-4' />
                <CarouselNext className='size-3 -right-4' />
              </Carousel>  
            </div>
          )
        }
        {message.role === 'assistant' && message.suggestion && (
          <div className="mt-2 w-full rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200">
            <div className="flex items-start">
              <Lightbulb className="mr-3 h-5 w-5 flex-shrink-0" />
              <div>
                <h4 className="font-semibold">Suggestion for your next response:</h4>
                {message.suggestion.grammar_rule && message.suggestion.grammar_rule?.toLowerCase() !== 'none' && (
                  <p className="mt-1">
                    <strong>Grammar focus:</strong> {message.suggestion.grammar_rule}
                  </p>
                )}
                {message.suggestion.next_response_ideas && message.suggestion.next_response_ideas.length > 0 && (
                  <div className="mt-2">
                    <strong>Ideas:</strong>
                    <ul className="list-disc pl-5">
                      {message.suggestion.next_response_ideas.map((idea, i) => (
                        <li key={i}>{idea}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      
      ))}
    </div>
  )

    console.log(messages)

  return (
    
    <TextSelectionActions
        chat={true}
        translate={true}
        smallDiplay={true} 
        actions={
          [
            ...basicActions,            
            actions.handleSynonyms,
            actions.handleExamples,
            actions.handleIPA,
            actions.handleAntonyms
          ]
        }
      className={cn(
        'ring-none flex h-full w-full flex-col items-stretch border-none',
        className
      )}
    >
      
      <div className="flex-1 content-center overflow-y-auto px-6">
        {messages.length ? messageList : header}
      </div>
      <form
        onSubmit={handleSubmit}
        className="border-input bg-background focus-within:ring-ring/10 relative mx-6 mb-6 flex flex-col items-stretch rounded-2xl border p-4 text-sm focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-0"
      >
        <div className="relative flex w-full flex-col">
          <AutoResizeTextarea
            onKeyDown={handleKeyDown}
            onChange={handleInputChange}
            value={input}
            placeholder="Enter a message"
            className="placeholder:text-muted-foreground flex-1 resize-none bg-transparent pr-12 focus:outline-none"
            disabled={isLoading}
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="submit"
                  variant="ghost"
                  size="icon"
                  className="absolute bottom-0 right-0 size-8 rounded-full"
                  disabled={!input.trim() || isLoading}
                >
                  <ArrowUpIcon size={16} />
                </Button>
              </TooltipTrigger>
              <TooltipContent sideOffset={12}>Submit</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="mt-4 flex items-center gap-4 border-t pt-4">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex-1 justify-between">
                Topics ({selectedTopics.length})
                <Divide className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
              <DialogHeader>
                <DialogTitle>Topics anpassen</DialogTitle>
                <DialogDescription>
                  Wählen Sie Topics aus und filtern Sie nach Tags, um das Chat-Erlebnis anzupassen.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-6 flex-1 overflow-hidden">
                {/* Left Column: Available Topics & Filters */}
                <div className="flex flex-col gap-4 overflow-hidden border-r pr-6">
                  <h3 className="text-lg font-medium">Verfügbare Topics</h3>
                  <Input
                    placeholder="Topics suchen..."
                    value={topicSearchTerm}
                    onChange={(e) => setTopicSearchTerm(e.target.value)}
                    className="w-full"
                  />
                  <div className="flex flex-wrap gap-2">
                    {allTags.map(tag => (
                      <div key={tag.id} className="flex items-center">
                        <Badge
                          variant={selectedTopicFilterTags.includes(tag.name) ? "default" : "secondary"}
                          onClick={() => handleTopicTagFilterToggle(tag.name)}
                          className="cursor-pointer rounded-r-none border-r-0"
                        >
                          {tag.name}
                        </Badge>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant={selectedTopicFilterTags.includes(tag.name) ? "default" : "secondary"}
                                size="icon"
                                className="h-6 w-6 rounded-l-none"
                                onClick={() => handleSelectTopicsByTag(tag.name)}
                              >
                                <PlusCircle className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Alle Topics mit diesem Tag auswählen</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" size="sm" onClick={handleAddAllVisibleTopics} disabled={availableTopics.length === 0}>
                    Alle sichtbaren auswählen
                  </Button>
                  <ScrollArea className="flex-1">
                    <div className="flex flex-col gap-2 pr-4">
                      {availableTopics.map((topicObj) => (
                        <div
                          key={topicObj.id}
                          onClick={() => setSelectedTopics(topicObj.value)}
                          className="flex justify-between items-center p-2 rounded-md hover:bg-accent cursor-pointer text-sm"
                        >
                          <div>
                            <span>{topicObj.value}</span>
                            {topicObj.tags && topicObj.tags.length > 0 && (
                              <div className="mt-1 flex flex-wrap gap-1">
                                {topicObj.tags.map(tag => (
                                  <Badge key={tag.id} variant="secondary" className="px-1 py-0 text-[10px]">
                                    {tag.name}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">Weight: {topicObj.weight}</span>
                        </div>
                      ))}
                      {availableTopics.length === 0 && (
                        <div className="text-center text-muted-foreground py-4">
                          Keine Topics gefunden
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </div>

                {/* Right Column: Selected Topics */}
                <div className="flex flex-col gap-4 overflow-hidden bg-slate-50 dark:bg-slate-900 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Ausgewählte Topics ({selectedTopics.length})</h3>
                    <Button variant="outline" size="sm" onClick={handleDeselectAllTopics} disabled={selectedTopics.length === 0}>
                      Alle abwählen
                    </Button>
                  </div>
                  <ScrollArea className="flex-1">
                    <div className="flex flex-col gap-2 pr-4">
                      {selectedTopics.map((topic) => (
                        <div key={topic} className="flex justify-between items-center p-2 rounded-md border bg-background text-sm">
                          <span>{topic}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => setSelectedTopics(topic)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {selectedTopics.length === 0 && (
                        <div className="text-center text-muted-foreground py-4">
                          Keine Topics ausgewählt
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog >
            <DialogTrigger asChild>
              <Button variant="outline" className="flex-1 justify-between">
                Grammatikregeln ({selectedGrammarRules.length})
                <Divide className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
              <DialogHeader>
                <DialogTitle>Grammatikregeln anpassen</DialogTitle>
                <DialogDescription>
                  Wählen Sie Regeln aus, passen Sie deren Gewichtung an und filtern Sie nach Tags, um das Chat-Erlebnis anzupassen.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-6 flex-1 overflow-hidden">
                {/* Left Column: Available Rules & Filters */}
                <div className="flex flex-col gap-4 overflow-hidden border-r pr-6">
                  <h3 className="text-lg font-medium">Verfügbare Regeln</h3>
                  <Input
                    placeholder="Regeln suchen..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                  <div className="flex flex-wrap gap-2">
                    {allTags.map(tag => (
                      <div key={tag.id} className="flex items-center">
                        <Badge
                          variant={selectedFilterTags.includes(tag.name) ? "default" : "secondary"}
                          onClick={() => handleTagFilterToggle(tag.name)}
                          className="cursor-pointer rounded-r-none border-r-0"
                        >
                          {tag.name}
                        </Badge>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant={selectedFilterTags.includes(tag.name) ? "default" : "secondary"}
                                size="icon"
                                className="h-6 w-6 rounded-l-none"
                                onClick={() => handleSelectByTag(tag.name)}
                              >
                                <PlusCircle className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Alle Regeln mit dem Tag "{tag.name}" hinzufügen</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" size="sm" onClick={handleAddAllVisibleRules} disabled={availableGrammarRules.length === 0}>
                    Alle sichtbaren auswählen
                  </Button>
                  <ScrollArea className="flex-1">
                    <div className="flex flex-col gap-2 pr-4">
                      {availableGrammarRules.map((ruleObj) => (
                        <div
                          key={ruleObj.value}
                          onClick={() => setSelectedGrammarRules(ruleObj)}
                          className="flex justify-between items-center p-2 rounded-md hover:bg-accent cursor-pointer text-sm"
                        >
                          <div>
                            <span>{ruleObj.value}</span>
                            {ruleObj.tags && ruleObj.tags.length > 0 && (
                              <div className="mt-1 flex flex-wrap gap-1">
                                {ruleObj.tags.map(tag => (
                                  <Badge key={tag.id} variant="secondary" className="px-1 py-0 text-[10px]">
                                    {tag.name}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">Gewicht: {ruleObj.weight}</span>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                  {/* Custom Rule */}
                  <div className="mt-auto pt-4 border-t">
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Eigene Regel hinzufügen</h3>
                    <div className="flex items-center gap-2">
                      <Input
                        type="text"
                        placeholder="Regelname"
                        className="flex-1"
                        value={newGrammarRule}
                        onChange={(e) => setNewGrammarRule(e.currentTarget.value)}
                      />
                      <Input
                        type="number"
                        placeholder="Gewicht"
                        className="w-24"
                        value={newGrammarRuleWeight}
                        onChange={(e) => setNewGrammarRuleWeight(parseInt(e.currentTarget.value) || 10)}
                        min="0"
                      />
                      <Button
                        onClick={async () => {
                          if (newGrammarRule.trim()) {
                            const customRule = { value: newGrammarRule.trim(), weight: newGrammarRuleWeight, tags: [] };
                            setSelectedGrammarRules(customRule);
                            try {
                              await fetch('/api/custom-settings', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ type: 'grammarRule', value: customRule.value, weight: customRule.weight }),
                              });
                              setAllGrammarRules(prev => [...prev, customRule]);
                              setNewGrammarRule('');
                              setNewGrammarRuleWeight(10);
                            } catch (error) {
                              console.error("Failed to save custom rule:", error);
                            }
                          }
                        }}
                      >
                        Hinzufügen
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Right Column: Selected Rules */}
                <div className="flex flex-col gap-4 overflow-hidden bg-slate-50 dark:bg-slate-900 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Ausgewählte Regeln ({selectedGrammarRules.length})</h3>
                    <Button variant="outline" size="sm" onClick={handleDeselectAllRules} disabled={selectedGrammarRules.length === 0}>
                      Alle abwählen
                    </Button>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded-md border bg-background">
                    <label htmlFor="grammarRuleCount" className="text-sm font-medium">
                      Anzahl zu verwendender Regeln:
                    </label>
                    <Input
                      id="grammarRuleCount"
                      type="number"
                      value={grammarRuleCount}
                      onChange={(e) => setGrammarRuleCount(parseInt(e.target.value) || 1)}
                      min="1"
                      max={selectedGrammarRules.length || 1}
                      className="w-20 text-right h-8"
                    />
                    <span className="text-xs text-muted-foreground">
                      (max: {selectedGrammarRules.length})
                    </span>
                  </div>
                  <ScrollArea className="flex-1">
                    <div className="flex flex-col gap-2 pr-4">
                      {selectedGrammarRules.map((ruleObj) => (
                        <div key={ruleObj.value} className="flex justify-between items-center p-2 rounded-md border bg-background text-sm">
                          <span>{ruleObj.value}</span>
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              value={ruleObj.weight}
                              onChange={(e) => {
                                const newWeight = parseInt(e.target.value) || 0;
                                const updatedRules = selectedGrammarRules.map(r =>
                                  r.value === ruleObj.value ? { ...r, weight: newWeight } : r
                                );
                                setSelectedGrammarRulesArray(updatedRules);
                              }}
                              onClick={(e) => e.stopPropagation()}
                              min="0"
                              className="w-20 text-right h-8"
                            />
                            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setSelectedGrammarRules(ruleObj)}>
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                      {selectedGrammarRules.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">Keine Regeln ausgewählt.</p>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button">Schließen</Button>
                </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </form>
    </TextSelectionActions>
  )
}


export const ChatManager = () => {
  const { isOpen } = useManageChat();
  console.log(isOpen)
  return (
    <>
      {isOpen && (
        <div className=" w-full h-full overflow-y-auto shadow-lg">
          <ChatForm />
        </div>
      )}
    </>
  );
};
